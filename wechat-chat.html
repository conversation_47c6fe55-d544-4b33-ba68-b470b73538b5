<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业微信聊天</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f0f0f0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 状态栏 */
        .status-bar {
            background: linear-gradient(135deg, #4a90e2, #5ba3f5);
            color: white;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .bar {
            width: 3px;
            background: white;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .wifi-icon, .battery {
            font-size: 12px;
        }

        /* 导航栏 */
        .nav-bar {
            background: linear-gradient(135deg, #4a90e2, #5ba3f5);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            font-size: 18px;
            cursor: pointer;
        }

        .group-info {
            text-align: center;
        }

        .group-title {
            font-size: 16px;
            font-weight: 500;
        }

        .group-subtitle {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 2px;
        }

        .nav-right {
            display: flex;
            gap: 15px;
        }

        .nav-icon {
            font-size: 18px;
            cursor: pointer;
        }

        /* 聊天区域 */
        .chat-area {
            flex: 1;
            background-color: #f0f0f0;
            padding: 20px;
            overflow-y: auto;
        }

        .time-stamp {
            text-align: center;
            color: #999;
            font-size: 12px;
            margin-bottom: 20px;
        }

        .message-group {
            margin-bottom: 20px;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 15px;
        }

        .message.right {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .avatar.user1 {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><rect width="40" height="40" fill="%23d4a574"/><text x="20" y="25" text-anchor="middle" fill="white" font-size="12">大</text></svg>');
        }

        .avatar.user2 {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><rect width="40" height="40" fill="%234a90e2"/><rect x="8" y="8" width="24" height="16" fill="white" rx="2"/><rect x="12" y="20" width="16" height="8" fill="white" rx="2"/><text x="20" y="32" text-anchor="middle" fill="white" font-size="8">商务</text></svg>');
        }

        .message-content {
            max-width: 250px;
        }

        .message-bubble {
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            font-size: 16px;
            line-height: 1.4;
        }

        .message.right .message-bubble {
            background: #95ec69;
        }

        /* 底部工具栏 */
        .bottom-toolbar {
            background: white;
            padding: 10px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .toolbar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            font-size: 11px;
            color: #666;
        }

        .toolbar-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .toolbar-item:nth-child(1) .toolbar-icon { background: #4CAF50; color: white; }
        .toolbar-item:nth-child(2) .toolbar-icon { background: #FFC107; color: white; }
        .toolbar-item:nth-child(3) .toolbar-icon { background: #FF9800; color: white; }
        .toolbar-item:nth-child(4) .toolbar-icon { background: #2196F3; color: white; }

        /* 输入区域 */
        .input-area {
            background: white;
            padding: 10px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .voice-btn, .emoji-btn, .add-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #ddd;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }

        .home-indicator {
            height: 4px;
            width: 134px;
            background: black;
            border-radius: 2px;
            margin: 8px auto;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <span>18:21</span>
            <span>🔔</span>
        </div>
        <div class="status-right">
            <div class="signal-bars">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>
            <span class="wifi-icon">📶</span>
            <span class="battery">58</span>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-left">
            <span class="back-btn">‹</span>
            <span>99+</span>
        </div>
        <div class="group-info">
            <div class="group-title">群聊(2)</div>
            <div class="group-subtitle">外部群 群主:商云Pay</div>
        </div>
        <div class="nav-right">
            <span class="nav-icon">📹</span>
            <span class="nav-icon">⋯</span>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-area">
        <div class="time-stamp">18:20</div>
        
        <div class="message right">
            <div class="avatar user2"></div>
            <div class="message-content">
                <div class="message-bubble">1</div>
            </div>
        </div>

        <div class="message">
            <div class="avatar user1"></div>
            <div class="message-content">
                <div class="message-bubble">1</div>
            </div>
        </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
        <div class="toolbar-item">
            <div class="toolbar-icon">💰</div>
            <span>发起收款</span>
        </div>
        <div class="toolbar-item">
            <div class="toolbar-icon">⚡</div>
            <span>快捷回复</span>
        </div>
        <div class="toolbar-item">
            <div class="toolbar-icon">👤</div>
            <span>推荐客服</span>
        </div>
        <div class="toolbar-item">
            <div class="toolbar-icon">📋</div>
            <span>商品问册</span>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
        <button class="voice-btn">🎤</button>
        <button class="emoji-btn">😊</button>
        <button class="add-btn">+</button>
    </div>

    <!-- Home指示器 -->
    <div class="home-indicator"></div>
</body>
</html>
